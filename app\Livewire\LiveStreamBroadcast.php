<?php

namespace App\Livewire;

use App\Models\LiveStream;
use App\Models\LiveStreamMessage;
use App\Models\LiveStreamDonation;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class LiveStreamBroadcast extends Component
{
    public $liveStream;
    public $title = '';
    public $description = '';
    public $isLive = false;
    public $viewers = 0;
    public $totalDonations = 0;
    public $messages = [];
    public $newMessage = '';
    public $showCreateModal = false;
    public $showEndModal = false;

    protected $listeners = [
        'live-stream-message' => 'addMessage',
        'live-stream-donation' => 'addDonation',
        'viewer-joined' => 'incrementViewers',
        'viewer-left' => 'decrementViewers',
    ];

    public function mount()
    {
        // Verificar se o usuário já tem uma live ativa
        $this->liveStream = LiveStream::where('user_id', Auth::id())
            ->whereIn('status', ['waiting', 'live'])
            ->first();

        if ($this->liveStream) {
            $this->title = $this->liveStream->title;
            $this->description = $this->liveStream->description;
            $this->isLive = $this->liveStream->isLive();
            $this->viewers = $this->liveStream->viewers_count;
            $this->totalDonations = $this->liveStream->total_donations;
            $this->loadMessages();
        }
    }

    public function createLiveStream()
    {
        $this->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
        ]);

        // Verificar se já existe uma live ativa
        $existingLive = LiveStream::where('user_id', Auth::id())
            ->whereIn('status', ['waiting', 'live'])
            ->first();

        if ($existingLive) {
            $this->addError('title', 'Você já tem uma live ativa. Termine a live atual antes de criar uma nova.');
            return;
        }

        $this->liveStream = LiveStream::create([
            'user_id' => Auth::id(),
            'title' => $this->title,
            'description' => $this->description,
            'status' => 'waiting',
        ]);

        $this->showCreateModal = false;
        $this->dispatch('live-stream-created', $this->liveStream->id);
        
        $this->dispatch(
            'toast',
            message: 'Live criada com sucesso! Configure sua câmera e clique em "Iniciar Live".',
            type: 'success'
        )->to('toast-notification');
    }

    public function startLive()
    {
        if (!$this->liveStream) {
            return;
        }

        $this->liveStream->start();
        $this->isLive = true;

        // Adicionar mensagem do sistema
        $this->addSystemMessage(Auth::user()->name . ' iniciou a live!');

        // Log para debug
        \Log::info('Live iniciada', ['stream_id' => $this->liveStream->id, 'user_id' => Auth::id()]);

        $this->dispatch('live-stream-started', $this->liveStream->id);

        $this->dispatch(
            'toast',
            message: 'Live iniciada com sucesso!',
            type: 'success'
        )->to('toast-notification');
    }

    public function endLive()
    {
        if (!$this->liveStream) {
            return;
        }

        $this->liveStream->end();
        $this->isLive = false;

        // Adicionar mensagem do sistema
        $this->addSystemMessage(Auth::user()->name . ' encerrou a live. Obrigado por assistir!');

        $this->dispatch('live-stream-ended', $this->liveStream->id);
        $this->showEndModal = false;
        
        $this->dispatch(
            'toast',
            message: 'Live encerrada com sucesso!',
            type: 'success'
        )->to('toast-notification');
    }

    public function sendMessage()
    {
        if (!$this->liveStream || empty(trim($this->newMessage))) {
            return;
        }

        $message = LiveStreamMessage::create([
            'live_stream_id' => $this->liveStream->id,
            'user_id' => Auth::id(),
            'message' => $this->newMessage,
            'type' => 'message',
        ]);

        $this->newMessage = '';
        $this->loadMessages();

        // Broadcast para todos os viewers
        $this->dispatch('live-stream-message', [
            'stream_id' => $this->liveStream->id,
            'message' => $message->toArray(),
            'user' => Auth::user()->toArray(),
        ]);
    }

    public function addMessage($data)
    {
        if ($data['stream_id'] == $this->liveStream?->id) {
            $this->loadMessages();
        }
    }

    public function addDonation($data)
    {
        if ($data['stream_id'] == $this->liveStream?->id) {
            $this->totalDonations = $this->liveStream->fresh()->total_donations;
            $this->loadMessages();
        }
    }

    public function incrementViewers()
    {
        $this->viewers++;
    }

    public function decrementViewers()
    {
        $this->viewers = max(0, $this->viewers - 1);
    }

    private function loadMessages()
    {
        if (!$this->liveStream) {
            return;
        }

        $this->messages = LiveStreamMessage::where('live_stream_id', $this->liveStream->id)
            ->with('user.userPhotos')
            ->latest()
            ->take(50)
            ->get()
            ->reverse()
            ->values()
            ->toArray();
    }

    private function addSystemMessage($message)
    {
        LiveStreamMessage::create([
            'live_stream_id' => $this->liveStream->id,
            'user_id' => Auth::id(),
            'message' => $message,
            'type' => 'system',
        ]);

        $this->loadMessages();
    }

    public function openCreateModal()
    {
        $this->showCreateModal = true;
    }

    public function closeCreateModal()
    {
        $this->showCreateModal = false;
        $this->reset(['title', 'description']);
    }

    public function openEndModal()
    {
        $this->showEndModal = true;
    }

    public function closeEndModal()
    {
        $this->showEndModal = false;
    }

    public function render()
    {
        return view('livewire.live-stream-broadcast');
    }
}
