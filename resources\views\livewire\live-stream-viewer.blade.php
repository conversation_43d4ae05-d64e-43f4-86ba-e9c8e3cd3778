<div class="min-h-screen bg-black text-white">
    <div class="flex h-screen">
        <!-- <PERSON><PERSON> do Vídeo -->
        <div class="flex-1 flex flex-col">
            <!-- Header da Live -->
            <div class="bg-zinc-900 p-4 flex justify-between items-center">
                <div class="flex items-center gap-4">
                    <img src="{{ $liveStream->user->userPhotos->first() ? Storage::url($liveStream->user->userPhotos->first()->photo_path) : asset('images/default-avatar.jpg') }}" 
                         class="w-10 h-10 rounded-full">
                    <div>
                        <h1 class="text-xl font-bold">{{ $liveStream->title }}</h1>
                        <p class="text-gray-400">{{ $liveStream->user->name }}</p>
                        @if($liveStream->description)
                            <p class="text-sm text-gray-500">{{ $liveStream->description }}</p>
                        @endif
                    </div>
                </div>
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2">
                        <x-flux::icon name="eye" class="w-5 h-5 text-purple-500" />
                        <span>{{ $liveStream->viewers_count }}</span>
                    </div>
                    @if($liveStream->isLive())
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                            <span class="text-red-500 font-bold">AO VIVO</span>
                        </div>
                    @elseif($liveStream->isEnded())
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                            <span class="text-gray-500 font-bold">ENCERRADA</span>
                        </div>
                    @else
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <span class="text-yellow-500 font-bold">AGUARDANDO</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Área do Vídeo -->
            <div class="flex-1 bg-black relative">
                <div id="video-container" class="w-full h-full flex items-center justify-center">
                    @if($liveStream->isLive())
                        <video id="remoteVideo" autoplay class="w-full h-full object-cover"></video>
                    @elseif($liveStream->isEnded())
                        <div class="text-center">
                            <x-flux::icon name="stop-circle" class="w-16 h-16 mx-auto mb-4 text-gray-500" />
                            <p class="text-gray-400 mb-2">Live Encerrada</p>
                            <p class="text-sm text-gray-500">Esta live foi encerrada pelo streamer</p>
                        </div>
                    @else
                        <div class="text-center">
                            <x-flux::icon name="clock" class="w-16 h-16 mx-auto mb-4 text-yellow-500" />
                            <p class="text-gray-400 mb-2">Aguardando Início</p>
                            <p class="text-sm text-gray-500">O streamer ainda não iniciou a transmissão</p>
                        </div>
                    @endif
                </div>

                <!-- Botão de Doação -->
                @if($liveStream->isLive())
                    <div class="absolute bottom-4 right-4">
                        <flux:button wire:click="openDonationModal" variant="primary" size="base">
                            <x-flux::icon name="heart" class="w-5 h-5 mr-2" />
                            Enviar Charm
                        </flux:button>
                    </div>
                @endif
            </div>
        </div>

        <!-- Chat da Live -->
        <div class="w-80 bg-zinc-900 flex flex-col">
            <!-- Header do Chat -->
            <div class="p-4 border-b border-zinc-700">
                <h3 class="font-bold">Chat da Live</h3>
            </div>

            <!-- Mensagens -->
            <div class="flex-1 overflow-y-auto p-4 space-y-3" id="chat-messages">
                @foreach($messages as $message)
                    <div class="flex gap-3">
                        @if($message['type'] === 'system')
                            <div class="w-full text-center">
                                <span class="text-yellow-400 text-sm">{{ $message['message'] }}</span>
                            </div>
                        @elseif($message['type'] === 'donation')
                            <div class="w-full bg-purple-900 bg-opacity-50 rounded-lg p-3">
                                <div class="flex items-center gap-2 mb-1">
                                    <img src="{{ $message['user']['user_photos'][0]['photo_path'] ?? asset('images/default-avatar.jpg') }}" 
                                         class="w-6 h-6 rounded-full">
                                    <span class="font-bold text-purple-300">{{ $message['user']['name'] }}</span>
                                    <x-flux::icon name="heart" class="w-4 h-4 text-red-500" />
                                </div>
                                <p class="text-sm">{{ $message['message'] }}</p>
                                <p class="text-xs text-purple-400 mt-1">
                                    Enviou {{ $message['data']['charm_name'] ?? 'um presente' }} 
                                    (R$ {{ number_format($message['data']['amount'] ?? 0, 2, ',', '.') }})
                                </p>
                            </div>
                        @else
                            <img src="{{ $message['user']['user_photos'][0]['photo_path'] ?? asset('images/default-avatar.jpg') }}" 
                                 class="w-8 h-8 rounded-full">
                            <div class="flex-1">
                                <div class="flex items-center gap-2">
                                    <span class="font-bold text-sm">{{ $message['user']['name'] }}</span>
                                    <span class="text-xs text-gray-400">
                                        {{ \Carbon\Carbon::parse($message['created_at'])->format('H:i') }}
                                    </span>
                                </div>
                                <p class="text-sm">{{ $message['message'] }}</p>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>

            <!-- Input de Mensagem -->
            @if($liveStream->isLive())
                <div class="p-4 border-t border-zinc-700">
                    <div class="flex gap-2">
                        <input 
                            wire:model="newMessage" 
                            wire:keydown.enter="sendMessage"
                            type="text" 
                            placeholder="Digite uma mensagem..."
                            class="flex-1 bg-zinc-800 border border-zinc-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                        >
                        <flux:button wire:click="sendMessage" variant="primary" size="sm">
                            <x-flux::icon name="paper-airplane" class="w-4 h-4" />
                        </flux:button>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Modal de Doação -->
    @if($showDonationModal)
        <flux:modal wire:model="showDonationModal" size="lg">
            <flux:modal.header>
                <flux:heading size="base">Enviar Charm para {{ $liveStream->user->name }}</flux:heading>
            </flux:modal.header>

            <flux:modal.body>
                <div class="space-y-6">
                    <!-- Saldo da Carteira -->
                    <div class="bg-zinc-800 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-300">Saldo da Carteira:</span>
                            <span class="text-green-400 font-bold">R$ {{ number_format($walletBalance, 2, ',', '.') }}</span>
                        </div>
                    </div>

                    <!-- Seleção de Charms -->
                    <div>
                        <p class="text-gray-300 mb-4">Escolha um charm para enviar:</p>
                        <div class="grid grid-cols-2 gap-3 mb-4">
                            @foreach($charms as $type => $charm)
                                <button
                                    wire:click="selectCharm('{{ $type }}')"
                                    class="p-4 border rounded-lg text-center hover:bg-gray-700 transition-colors
                                        {{ $selectedCharm == $type ? 'bg-purple-900 border-purple-500' : 'border-zinc-600' }}"
                                >
                                    <div class="flex flex-col items-center">
                                        <x-flux::icon name="{{ $charm['icon'] }}" class="w-8 h-8 mb-2 {{ $selectedCharm == $type ? 'text-purple-400' : 'text-gray-400' }}" />
                                        <div class="font-bold text-gray-300">{{ $charm['name'] }}</div>
                                        <div class="text-sm text-gray-400">R$ {{ number_format($charm['price'], 2, ',', '.') }}</div>
                                        <div class="text-xs text-gray-500 mt-1">{{ $charm['description'] }}</div>
                                    </div>
                                </button>
                            @endforeach
                        </div>
                    </div>

                    <!-- Mensagem Opcional -->
                    <div>
                        <flux:field>
                            <flux:label>Mensagem (opcional)</flux:label>
                            <flux:textarea wire:model="donationMessage" placeholder="Deixe uma mensagem especial..." rows="3" />
                        </flux:field>
                    </div>

                    @error('donation')
                        <div class="text-red-400 text-sm">{{ $message }}</div>
                    @enderror
                </div>
            </flux:modal.body>

            <flux:modal.footer>
                <flux:button wire:click="closeDonationModal" variant="ghost">Cancelar</flux:button>
                <flux:button 
                    wire:click="sendDonation" 
                    variant="primary" 
                    :disabled="!$selectedCharm || $processing"
                >
                    @if($processing)
                        <x-flux::icon name="arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                        Processando...
                    @else
                        <x-flux::icon name="heart" class="w-4 h-4 mr-2" />
                        Enviar Charm
                        @if($selectedCharm)
                            (R$ {{ number_format($charms[$selectedCharm]['price'], 2, ',', '.') }})
                        @endif
                    @endif
                </flux:button>
            </flux:modal.footer>
        </flux:modal>
    @endif
</div>

@script
<script>
// Auto-scroll do chat
function scrollChatToBottom() {
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// Observer para auto-scroll quando novas mensagens chegarem
const observer = new MutationObserver(scrollChatToBottom);
const chatContainer = document.getElementById('chat-messages');
if (chatContainer) {
    observer.observe(chatContainer, { childList: true, subtree: true });
}

// Simular conexão WebRTC para receber stream (em produção seria implementado com servidor de streaming)
document.addEventListener('livewire:init', () => {
    // Aqui seria implementada a lógica para receber o stream do broadcaster
    // Por enquanto, vamos simular com uma mensagem
    console.log('Viewer conectado à live:', @js($liveStream->id));
});
</script>
@endscript
