<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta name="csrf-token" content="{{ csrf_token() }}">

@if(app()->environment('local'))
<!-- Content Security Policy para desenvolvimento local (mais permissivo) -->
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self' 'unsafe-inline' 'unsafe-eval' https: http: data: blob: mediastream:;
    connect-src *;
    media-src 'self' blob: data: mediastream:;
    img-src 'self' https: http: data: blob:;
    font-src 'self' https: http: data:;
    style-src 'self' 'unsafe-inline' https: http:;
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:;
">
@else
<!-- Content Security Policy para produção (mais restritivo) -->
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:;
    connect-src 'self' https: wss: ws:
        https://peerjs-server.herokuapp.com wss://peerjs-server.herokuapp.com
        https://0.peerjs.com wss://0.peerjs.com
        https://1.peerjs.com wss://1.peerjs.com
        https://www.swingcuritiba.com.br wss://www.swingcuritiba.com.br
        https://www.swingcuritiba.com.br:9000 wss://www.swingcuritiba.com.br:9000
        https://stun.l.google.com:19302
        https://stun1.l.google.com:19302
        https://stun2.l.google.com:19302
        https://stun3.l.google.com:19302
        https://stun4.l.google.com:19302;
    media-src 'self' blob: data: mediastream:;
    img-src 'self' https: data: blob:;
    font-src 'self' https: data:;
    style-src 'self' 'unsafe-inline' https:;
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https:;
">
@endif

<title>{{ $title ?? 'Desiree Swing Club - Curitiba' }}</title>

<link rel="preconnect" href="https://fonts.bunny.net">
<link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

@vite(['resources/css/app.css', 'resources/js/app.js'])

<!-- CSS personalizado para galeria -->
<link rel="stylesheet" href="{{ asset('css/gallery-animations.css') }}">

<!-- Scripts personalizados -->
@auth
<!-- Script de geolocalização automática -->
<script src="{{ asset('js/auto-geolocation.js') }}"></script>
@endauth
