/**
 * Serviço de Streaming Real com WebRTC e Gravação
 * Usando PeerJS para conexões P2P gratuitas
 */

import Peer from 'peerjs';

class StreamingService {
    constructor() {
        this.peer = null;
        this.localStream = null;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.connections = new Map();
        this.isStreaming = false;
        this.isRecording = false;
        this.streamId = null;
        this.startTime = null;

        // Configurações de qualidade
        this.videoConstraints = {
            width: { min: 640, ideal: 1280, max: 1920 },
            height: { min: 480, ideal: 720, max: 1080 },
            frameRate: { min: 15, ideal: 30, max: 60 }
        };

        this.audioConstraints = {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 44100
        };
    }

    /**
     * Inicializar o serviço de streaming
     */
    async initialize(streamId) {
        try {
            this.streamId = streamId;
            console.log('🚀 Inicializando streaming service para:', streamId);

            // Detectar ambiente e configurar servidores PeerJS adequados
            const isProduction = window.location.hostname === 'www.swingcuritiba.com.br';
            const isLocal = window.location.hostname === 'desiree2.test';
            const isSecure = window.location.protocol === 'https:';

            const peerServers = [];

            if (isLocal) {
                // Ambiente de desenvolvimento (Herd)
                peerServers.push({
                    host: 'desiree2.test',
                    port: 9000,
                    path: '/peerjs',
                    secure: false
                });
            }

            if (isProduction) {
                // Ambiente de produção - usar servidores externos confiáveis
                peerServers.push({
                    host: 'www.swingcuritiba.com.br',
                    port: 9000,
                    path: '/peerjs',
                    secure: true
                });
            }

            // Servidores externos como fallback
            peerServers.push(
                {
                    host: 'peerjs-server.herokuapp.com',
                    port: 443,
                    path: '/',
                    secure: true
                },
                {
                    host: '0.peerjs.com',
                    port: 443,
                    path: '/',
                    secure: true
                }
            );

            for (let i = 0; i < peerServers.length; i++) {
                try {
                    console.log(`🔄 Tentando servidor ${i + 1}:`, peerServers[i].host);

                    this.peer = new Peer(streamId, {
                        ...peerServers[i],
                        config: {
                            iceServers: [
                                { urls: 'stun:stun.l.google.com:19302' },
                                { urls: 'stun:stun1.l.google.com:19302' },
                                { urls: 'stun:stun2.l.google.com:19302' },
                                { urls: 'stun:stun3.l.google.com:19302' },
                                { urls: 'stun:stun4.l.google.com:19302' }
                            ]
                        }
                    });

                    const result = await new Promise((resolve, reject) => {
                        const timeout = setTimeout(() => {
                            reject(new Error('Timeout na conexão'));
                        }, 5000);

                        this.peer.on('open', (id) => {
                            clearTimeout(timeout);
                            console.log('✅ Peer conectado com ID:', id, 'no servidor:', peerServers[i].host);
                            this.setupPeerEvents();
                            resolve(id);
                        });

                        this.peer.on('error', (error) => {
                            clearTimeout(timeout);
                            console.error('❌ Erro no servidor', peerServers[i].host, ':', error);
                            reject(error);
                        });
                    });

                    return result; // Sucesso, retornar
                } catch (error) {
                    console.warn(`⚠️ Falha no servidor ${i + 1}:`, error.message);
                    if (this.peer) {
                        this.peer.destroy();
                        this.peer = null;
                    }

                    // Se é o último servidor, lançar erro
                    if (i === peerServers.length - 1) {
                        throw new Error('Todos os servidores PeerJS falharam');
                    }
                }
            }
        } catch (error) {
            console.error('❌ Erro ao inicializar streaming:', error);
            throw error;
        }
    }

    /**
     * Configurar eventos do Peer
     */
    setupPeerEvents() {
        // Quando alguém se conecta para assistir
        this.peer.on('call', (call) => {
            console.log('🔗 Recebendo chamada de viewer:', call.peer);

            if (this.localStream) {
                console.log('📹 Respondendo chamada com stream local');
                call.answer(this.localStream);
                this.connections.set(call.peer, call);

                call.on('close', () => {
                    console.log('❌ Viewer desconectado:', call.peer);
                    this.connections.delete(call.peer);
                    this.updateViewerCount();
                });

                call.on('error', (error) => {
                    console.error('❌ Erro na conexão com viewer:', call.peer, error);
                    this.connections.delete(call.peer);
                    this.updateViewerCount();
                });

                // Monitorar estado da conexão
                call.peerConnection.onconnectionstatechange = () => {
                    console.log('Estado da conexão com', call.peer, ':', call.peerConnection.connectionState);

                    if (call.peerConnection.connectionState === 'connected') {
                        console.log('✅ Viewer conectado com sucesso:', call.peer);
                    } else if (call.peerConnection.connectionState === 'failed' ||
                        call.peerConnection.connectionState === 'disconnected') {
                        console.log('❌ Conexão perdida com viewer:', call.peer);
                        this.connections.delete(call.peer);
                        this.updateViewerCount();
                    }
                };

                this.updateViewerCount();
                console.log('👥 Total de viewers conectados:', this.connections.size);
            } else {
                console.warn('⚠️ Tentativa de conexão sem stream local ativo');
                call.close();
            }
        });

        // Quando alguém se desconecta
        this.peer.on('disconnected', () => {
            console.log('📡 Peer broadcaster desconectado');
        });

        // Erro no peer
        this.peer.on('error', (error) => {
            console.error('❌ Erro no peer broadcaster:', error);
        });
    }

    /**
     * Iniciar captura de câmera e microfone
     */
    async startCamera() {
        try {
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: this.videoConstraints,
                audio: this.audioConstraints
            });

            console.log('Câmera iniciada com sucesso');
            return this.localStream;
        } catch (error) {
            console.error('Erro ao acessar câmera:', error);
            throw error;
        }
    }

    /**
     * Iniciar streaming
     */
    async startStreaming() {
        try {
            if (!this.localStream) {
                await this.startCamera();
            }

            this.isStreaming = true;
            this.startTime = new Date();

            console.log('Streaming iniciado');

            // Notificar servidor Laravel sobre início do stream
            await this.notifyServerStreamStart();

            return true;
        } catch (error) {
            console.error('Erro ao iniciar streaming:', error);
            throw error;
        }
    }

    /**
     * Iniciar gravação do stream
     */
    async startRecording() {
        try {
            if (!this.localStream) {
                throw new Error('Stream não iniciado');
            }

            // Configurações de gravação otimizadas
            const options = {
                mimeType: 'video/webm;codecs=vp9,opus',
                videoBitsPerSecond: 2500000, // 2.5 Mbps
                audioBitsPerSecond: 128000   // 128 kbps
            };

            // Fallback para navegadores que não suportam VP9
            if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                options.mimeType = 'video/webm;codecs=vp8,opus';
            }

            this.mediaRecorder = new MediaRecorder(this.localStream, options);
            this.recordedChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.saveRecording();
            };

            this.mediaRecorder.start(1000); // Salvar chunk a cada 1 segundo
            this.isRecording = true;

            console.log('Gravação iniciada');
            return true;
        } catch (error) {
            console.error('Erro ao iniciar gravação:', error);
            throw error;
        }
    }

    /**
     * Parar streaming
     */
    async stopStreaming() {
        try {
            this.isStreaming = false;

            // Parar gravação se estiver ativa
            if (this.isRecording) {
                await this.stopRecording();
            }

            // Fechar todas as conexões
            this.connections.forEach((call) => {
                call.close();
            });
            this.connections.clear();

            // Parar stream local
            if (this.localStream) {
                this.localStream.getTracks().forEach(track => {
                    track.stop();
                });
                this.localStream = null;
            }

            // Notificar servidor sobre fim do stream
            await this.notifyServerStreamEnd();

            console.log('Streaming parado');
            return true;
        } catch (error) {
            console.error('Erro ao parar streaming:', error);
            throw error;
        }
    }

    /**
     * Parar gravação
     */
    async stopRecording() {
        try {
            if (this.mediaRecorder && this.isRecording) {
                this.mediaRecorder.stop();
                this.isRecording = false;
                console.log('Gravação parada');
            }
        } catch (error) {
            console.error('Erro ao parar gravação:', error);
            throw error;
        }
    }

    /**
     * Salvar gravação no servidor
     */
    async saveRecording() {
        try {
            if (this.recordedChunks.length === 0) {
                console.warn('Nenhum dado gravado para salvar');
                return;
            }

            const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
            const formData = new FormData();

            formData.append('video', blob, `live-${this.streamId}-${Date.now()}.webm`);
            formData.append('stream_id', this.streamId);
            formData.append('duration', this.getStreamDuration());

            const response = await fetch('/api/live-streams/save-recording', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (response.ok) {
                const result = await response.json();
                console.log('Gravação salva com sucesso:', result);
                return result;
            } else {
                throw new Error('Erro ao salvar gravação no servidor');
            }
        } catch (error) {
            console.error('Erro ao salvar gravação:', error);
            throw error;
        }
    }

    /**
     * Conectar como viewer
     */
    async connectAsViewer(streamId) {
        try {
            console.log('🔗 Tentando conectar como viewer ao stream:', streamId);

            // Usar a mesma lógica de detecção de ambiente para viewers
            const isProduction = window.location.hostname === 'www.swingcuritiba.com.br';
            const isLocal = window.location.hostname === 'desiree2.test';
            const isSecure = window.location.protocol === 'https:';

            const peerServers = [];

            if (isLocal) {
                // Ambiente de desenvolvimento (Herd)
                peerServers.push({
                    host: 'desiree2.test',
                    port: 9000,
                    path: '/peerjs',
                    secure: false
                });
            }

            if (isProduction) {
                // Ambiente de produção
                peerServers.push({
                    host: 'www.swingcuritiba.com.br',
                    port: 9000,
                    path: '/peerjs',
                    secure: true
                });
            }

            // Servidores externos como fallback
            peerServers.push(
                {
                    host: 'peerjs-server.herokuapp.com',
                    port: 443,
                    path: '/',
                    secure: true
                },
                {
                    host: '0.peerjs.com',
                    port: 443,
                    path: '/',
                    secure: true
                }
            );

            // Criar novo peer para o viewer
            const viewerId = 'viewer-' + Math.random().toString(36).substr(2, 9);

            for (let i = 0; i < peerServers.length; i++) {
                try {
                    console.log(`🔄 Viewer tentando servidor ${i + 1}:`, peerServers[i].host);

                    this.peer = new Peer(viewerId, {
                        ...peerServers[i],
                        config: {
                            iceServers: [
                                { urls: 'stun:stun.l.google.com:19302' },
                                { urls: 'stun:stun1.l.google.com:19302' },
                                { urls: 'stun:stun2.l.google.com:19302' },
                                { urls: 'stun:stun3.l.google.com:19302' },
                                { urls: 'stun:stun4.l.google.com:19302' }
                            ]
                        }
                    });

                    return new Promise((resolve, reject) => {
                        this.peer.on('open', (id) => {
                            console.log('Viewer conectado com ID:', id);
                            console.log('Tentando chamar broadcaster:', streamId);

                            // Tentar conectar ao broadcaster
                            const call = this.peer.call(streamId, null);

                            if (!call) {
                                reject(new Error('Não foi possível iniciar chamada para o broadcaster'));
                                return;
                            }

                            call.on('stream', (remoteStream) => {
                                console.log('✅ Stream recebido do broadcaster!');
                                console.log('Stream tracks:', remoteStream.getTracks());
                                resolve(remoteStream);
                            });

                            call.on('error', (error) => {
                                console.error('❌ Erro na chamada:', error);
                                reject(error);
                            });

                            call.on('close', () => {
                                console.log('Chamada encerrada pelo broadcaster');
                            });

                            // Timeout para conexão
                            setTimeout(() => {
                                if (call.peerConnection.connectionState !== 'connected') {
                                    console.warn('Timeout na conexão com broadcaster');
                                    reject(new Error('Timeout na conexão com o broadcaster'));
                                }
                            }, 10000); // 10 segundos
                        });

                        this.peer.on('error', (error) => {
                            console.error('❌ Erro no peer viewer:', error);
                            reject(error);
                        });

                        this.peer.on('disconnected', () => {
                            console.log('Peer viewer desconectado');
                        });
                    });

                    // Se chegou aqui, sucesso
                    return result;
                } catch (error) {
                    console.warn(`⚠️ Falha no servidor ${i + 1} para viewer:`, error.message);
                    if (this.peer) {
                        this.peer.destroy();
                        this.peer = null;
                    }

                    // Se é o último servidor, lançar erro
                    if (i === peerServers.length - 1) {
                        throw new Error('Todos os servidores PeerJS falharam para viewer');
                    }
                }
            }
        } catch (error) {
            console.error('❌ Erro ao conectar como viewer:', error);
            throw error;
        }
    }

    /**
     * Atualizar contador de viewers
     */
    updateViewerCount() {
        const count = this.connections.size;

        // Notificar servidor sobre mudança no número de viewers
        fetch('/api/live-streams/update-viewers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                stream_id: this.streamId,
                viewers_count: count
            })
        });

        // Disparar evento para atualizar UI
        window.dispatchEvent(new CustomEvent('viewersUpdated', {
            detail: { count }
        }));
    }

    /**
     * Obter duração do stream em segundos
     */
    getStreamDuration() {
        if (!this.startTime) return 0;
        return Math.floor((new Date() - this.startTime) / 1000);
    }

    /**
     * Notificar servidor sobre início do stream
     */
    async notifyServerStreamStart() {
        try {
            await fetch('/api/live-streams/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    stream_id: this.streamId
                })
            });
        } catch (error) {
            console.error('Erro ao notificar servidor:', error);
        }
    }

    /**
     * Notificar servidor sobre fim do stream
     */
    async notifyServerStreamEnd() {
        try {
            await fetch('/api/live-streams/end', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    stream_id: this.streamId,
                    duration: this.getStreamDuration()
                })
            });
        } catch (error) {
            console.error('Erro ao notificar servidor:', error);
        }
    }

    /**
     * Destruir instância
     */
    destroy() {
        if (this.peer) {
            this.peer.destroy();
        }

        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
        }

        this.connections.clear();
    }
}

// Exportar para uso global
window.StreamingService = StreamingService;

export default StreamingService;
