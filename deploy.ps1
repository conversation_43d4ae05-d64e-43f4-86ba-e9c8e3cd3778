# Script de Deploy para Produção - swingcuritiba.com.br
# Execute este script para preparar os arquivos para upload via FTP

Write-Host "🚀 Iniciando processo de deploy para produção..." -ForegroundColor Green

# 1. Fazer backup do .env atual
Write-Host "📦 Fazendo backup do .env atual..." -ForegroundColor Yellow
if (Test-Path ".env.backup") {
    Remove-Item ".env.backup"
}
Copy-Item ".env" ".env.backup"

# 2. Copiar configurações de produção
Write-Host "⚙️ Aplicando configurações de produção..." -ForegroundColor Yellow
Copy-Item ".env.production" ".env"

# 3. Limpar cache do Laravel
Write-Host "🧹 Limpando cache do Laravel..." -ForegroundColor Yellow
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

# 4. Otimizar para produção
Write-Host "⚡ Otimizando para produção..." -ForegroundColor Yellow
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 5. Build dos assets
Write-Host "🔨 Compilando assets para produção..." -ForegroundColor Yellow
npm run build

# 6. Verificar se o build foi bem-sucedido
if (Test-Path "public/build/manifest.json") {
    Write-Host "✅ Assets compilados com sucesso!" -ForegroundColor Green
} else {
    Write-Host "❌ Erro na compilação dos assets!" -ForegroundColor Red
    exit 1
}

# 7. Criar lista de arquivos para upload
Write-Host "📋 Criando lista de arquivos para upload..." -ForegroundColor Yellow
$uploadFiles = @(
    "app/",
    "bootstrap/",
    "config/",
    "database/",
    "public/",
    "resources/views/",
    "routes/",
    "storage/app/",
    "storage/framework/",
    ".env",
    "artisan",
    "composer.json",
    "composer.lock"
)

Write-Host "📁 Arquivos prontos para upload via FTP:" -ForegroundColor Cyan
foreach ($file in $uploadFiles) {
    if (Test-Path $file) {
        Write-Host "  ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "  ⚠ $file (não encontrado)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎯 PRÓXIMOS PASSOS:" -ForegroundColor Magenta
Write-Host "1. Faça upload dos arquivos listados acima via FTP" -ForegroundColor White
Write-Host "2. No servidor, execute: php artisan migrate --force" -ForegroundColor White
Write-Host "3. Configure as permissões: chmod -R 755 storage bootstrap/cache" -ForegroundColor White
Write-Host "4. Teste o site: https://swingcuritiba.com.br" -ForegroundColor White
Write-Host ""
Write-Host "📧 CONFIGURAÇÕES DE EMAIL:" -ForegroundColor Magenta
Write-Host "SMTP: smtp.kinghost.net:587" -ForegroundColor White
Write-Host "User: <EMAIL>" -ForegroundColor White
Write-Host ""
Write-Host "🔄 Para voltar ao desenvolvimento local, execute:" -ForegroundColor Magenta
Write-Host "Copy-Item '.env.backup' '.env'" -ForegroundColor White
Write-Host "php artisan config:clear" -ForegroundColor White

Write-Host ""
Write-Host "✅ Deploy preparado com sucesso!" -ForegroundColor Green
